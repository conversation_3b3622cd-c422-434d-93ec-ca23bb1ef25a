import {
  useLoaderData,
  useSubmit,
  useNavigation,
  useActionData,
} from "react-router";
import { LoaderFunctionArgs, ActionFunctionArgs, data } from "react-router";
import { Configuration, PreferencesApi } from "~/api/openapi/generated";
import { configurationParameters } from "~/api/openapi/configParams";
import PreferenceForm from "./components/PreferenceForm";
import { toast } from "react-toastify";
import { useEffect } from "react";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const config = new Configuration(await configurationParameters(request));
  const preferences = await new PreferencesApi(
    config
  ).preferencesGetPreferenceSchemas();

  if (!params.preference_page) {
    return data({ selectedSchema: null, scope: null });
  }

  // Find the selected preference schema by title
  // Convert the URL param to a title format for matching
  const lowercasedPreferenceTitle = params.preference_page
    .replace("-", " ")
    .toLowerCase();

  const scope = params.scope === "organization" ? "organization" : "user";
  const preferenceSchemas =
    scope === "organization"
      ? preferences.orgPreferences
      : preferences.userPreferences;
  const selectedSchema = preferenceSchemas.find(
    (schema) => schema.title.toLowerCase() === lowercasedPreferenceTitle
  );

  return { selectedSchema, scope };
}

export async function action({ request }: ActionFunctionArgs) {
  const config = new Configuration(await configurationParameters(request));
  const preferencesApi = new PreferencesApi(config);

  try {
    const formData = await request.formData();
    const preferenceUpdate = JSON.parse(formData.get("json") as string);
    await preferencesApi.preferencesUpdatePreferences({ preferenceUpdate });
    return data({ success: true });
  } catch (error) {
    return data({ success: false }, { status: 400 });
  }
}

export default function PreferencePage() {
  const { selectedSchema, scope } = useLoaderData<typeof loader>();
  const { success } = useActionData<typeof action>() || {};
  const submit = useSubmit();
  const navigation = useNavigation();

  const toastId = `update-preferences-${selectedSchema?.title}`;

  useEffect(() => {
    if (navigation.state === "submitting") {
      toast.loading("Saving preferences...", {
        toastId,
        position: "top-right",
      });
    } else if (navigation.state === "idle") {
      if (!success) {
        toast.update(toastId, {
          render: "Failed to save preferences",
          type: "error",
          isLoading: false,
          closeButton: true,
          autoClose: 2000,
        });
        return;
      }
      toast.update(toastId, {
        render: "Preferences saved successfully!",
        type: "success",
        isLoading: false,
        closeButton: true,
        autoClose: 2000,
      });
    }
  }, [navigation.state, navigation.formMethod, success, toastId]);

  if (!selectedSchema) {
    return (
      <div className="w-full">
        <h1 className="mb-6 text-2xl font-semibold">Preferences</h1>
        <p>
          Select a preference category from the menu to configure your settings.
        </p>
      </div>
    );
  }

  return (
    <PreferenceForm
      key={`${selectedSchema.title}-${scope}`}
      title={selectedSchema.title}
      jsonSchema={selectedSchema.jsonSchema}
      uiSchema={selectedSchema.uiSchema}
      data={selectedSchema.data}
      onSubmit={(data) => {
        const formData = new FormData();
        formData.append("json", JSON.stringify({ ...data, scope }));
        submit(formData, { method: "post", replace: true });
      }}
    />
  );
}
