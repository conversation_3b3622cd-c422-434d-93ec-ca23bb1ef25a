import {
  type LoaderFunctionArgs,
  type ActionFunction,
  useLoaderData,
  useSubmit,
  useRevalidator,
  useParams,
  MetaFunction,
} from "react-router";
import { BackButton } from "~/@ui/buttons/BackButton";
import {
  BanknoteArrowDown,
  BanknoteArrowUp,
  Building2,
  Calendar,
  CircleCheckBig,
  ClipboardList,
  Clock,
  DollarSign,
  Landmark,
  PiggyBank,
  Tag,
  ThumbsUp,
} from "lucide-react";
import "react-toastify/dist/ReactToastify.min.css";
import { toast, Id } from "react-toastify";
import { HeaderV2, SidebarV2, PageTitleText } from "~/@ui/layout/LayoutV2";
import { Button } from "~/@shadcn/ui/button";
import { Separator } from "~/@shadcn/ui/separator";
import { ClientTabGroup } from "~/routes/_auth.clients.$id._index/ClientsTabGroup";
import { NotesTab } from "./NotesTab";
import { TasksTab } from "./TasksTab";
import { ClientRecapTab } from "./ClientRecapTab";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { useEffect, useRef, useState } from "react";
import { Typography } from "~/@ui/Typography";
import { Badge } from "~/@shadcn/ui/badge";
import { capitalize } from "~/utils/strings";
import { mockData } from "~/api/clients/mockData";
import { useFlag } from "~/context/flags";
import { formatCurrency } from "~/utils";
import { format } from "date-fns";
import { AfterHydration } from "~/utils/hydration";
import AskMeAnythingForClient from "~/@ui/AskMeAnythingForClient";
import {
  ClientApi,
  ClientRecapStatus,
  Configuration,
  SearchApi,
  TaskResponse,
} from "~/api/openapi/generated";
import { SerializeFrom } from "~/types/remix";
import { configurationParameters } from "~/api/openapi/configParams";
import { z } from "zod";

const ClientBasicInfoStruct = z.object({
  gross_annual_income: z.number(),
  assets: z.number(),
  liabilities: z.number(),
  non_liquid_assets: z.number(),
});

const usePollingOnPrepStatus = (status: ClientRecapStatus | null) => {
  const revalidator = useRevalidator();

  useEffect(() => {
    if (status !== ClientRecapStatus.Processing) return;

    const intervalID = setInterval(() => {
      if (navigator.onLine) {
        revalidator.revalidate();
      }
    }, 10_000);

    return () => clearInterval(intervalID);
  }, [status, revalidator]);
};

// Constants
const ERROR_MISSING_PARAMETER = 'Missing route parameter "id"';

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  if (!params.id) throw new Error(ERROR_MISSING_PARAMETER);

  const configuration = new Configuration(
    await configurationParameters(request)
  );
  const client = await new ClientApi(configuration).clientGetClient({
    clientId: params.id,
  });
  return { client };
};

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `Client - ${data?.client.name ?? "view details"}` },
    { name: "description", content: "View and edit client" },
  ];
};

export const action: ActionFunction = async ({ request, params }) => {
  try {
    if (!params.id) throw new Error(ERROR_MISSING_PARAMETER);
    const clientId = params.id;
    const contentType = request.headers.get("content-type");

    let data;
    if (contentType && contentType.includes("application/json")) {
      data = await request.json();
    } else if (contentType && contentType.includes("multipart/form-data")) {
      const formData = await request.formData();
      data = Object.fromEntries(formData.entries());
    } else {
      throw new Error("Unsupported content type");
    }
    const configuration = new Configuration(
      await configurationParameters(request)
    );

    switch (data.actionType) {
      case "search-client":
        const { answer } = await new SearchApi(
          configuration
        ).searchSearchClient({
          clientId,
          query: data.query,
        });
        return {
          actionType: "search-client",
          success: true,
          answer: answer,
        };
      case "generate-meeting-prep":
        const response = await new ClientApi(
          configuration
        ).clientGenerateClientRecap({ clientId });
        return { success: response.status !== ClientRecapStatus.Failed };
      default:
        throw new Error(`Unsupported action type: ${data.actionType}`);
    }
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

const Route = () => {
  const { id } = useParams();
  const { client } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const toastId = useRef<Id | null>(null);
  const DemoClientMockDataEnabled = useFlag("DemoClientMockDataEnabled");
  const [query, setQuery] = useState("");
  const [triggerSearch, setTriggerSearch] = useState(false);

  const handleSyncClick = () => {
    const formData = new FormData();
    formData.append("actionType", "generate-meeting-prep");
    submit(formData, {
      method: "post",
      action: `/clients/${id}`,
      encType: "multipart/form-data",
    });

    if (toastId.current === null || !toast.isActive(toastId.current)) {
      toastId.current = toast.info(
        "Generating client recap. This may take a few moments...",
        {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "colored",
        }
      );
    }
  };

  const result = ClientBasicInfoStruct.safeParse(client.basicInfo);
  const basicInfo = result.success ? result.data : undefined;

  usePollingOnPrepStatus(client.recapStatus);

  return (
    <SidebarV2
      favorSidebarOnMobile
      header={
        <HeaderV2
          className="lg:gap-0"
          left={
            <BackButton
              className="lg:hidden"
              to="/clients"
              tooltip="Back to clients"
            />
          }
          right={
            <div className="flex w-full items-center gap-2">
              <AskMeAnythingForClient
                clientId={id!}
                query={query}
                setQuery={setQuery}
                triggerSearch={triggerSearch}
                setTriggerSearch={setTriggerSearch}
              />
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={handleSyncClick}
                    className="flex items-center justify-center space-x-2 rounded-lg bg-green-500 px-4 py-2 text-white transition-transform duration-300 hover:bg-green-600 active:bg-green-700"
                    disabled={
                      client.recapStatus === ClientRecapStatus.Processing
                    }
                  >
                    <ClipboardList className="!h-5 !w-5" />
                    Generate Client Recap
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Generate Client Recap</TooltipContent>
              </Tooltip>
            </div>
          }
        />
      }
    >
      <div className="flex flex-col gap-3 self-stretch px-6 pb-6">
        <div className="flex items-center justify-between">
          <PageTitleText className="text-2xl font-semibold text-gray-800">
            {client.name}
          </PageTitleText>
          {(
            [
              ClientRecapStatus.Processing,
              ClientRecapStatus.Failed,
            ] as (ClientRecapStatus | null)[]
          ).includes(client.recapStatus) && (
            <div
              className={`flex items-center rounded-md px-3 py-1 opacity-80 shadow-md ${
                client.recapStatus === ClientRecapStatus.Processing
                  ? "bg-primary"
                  : "bg-red-600"
              }`}
            >
              <Typography className="whitespace-nowrap text-sm text-white">
                {client.recapStatus === ClientRecapStatus.Processing
                  ? "Client recap generation in progress"
                  : "Client recap generation failed. Please try again."}
              </Typography>
            </div>
          )}
        </div>
        {!DemoClientMockDataEnabled && basicInfo && (
          <div className="grid grid-cols-2 gap-2">
            {basicInfo.gross_annual_income && (
              <Typography className="flex items-center text-gray-600">
                <BanknoteArrowUp size={20} className="mr-2 text-green-500" />
                <span className="text-sm font-medium">
                  Gross Annual Income:
                </span>
                <span className="ml-auto">
                  {formatCurrency(basicInfo.gross_annual_income)}
                </span>
              </Typography>
            )}

            {basicInfo.assets && (
              <Typography className="flex items-center text-gray-600">
                <PiggyBank size={20} className="mr-2 text-blue-500" />
                <span className="text-sm font-medium">Assets:</span>
                <span className="ml-auto">
                  {formatCurrency(basicInfo.assets)}
                </span>
              </Typography>
            )}

            {basicInfo.liabilities && (
              <Typography className="flex items-center text-gray-600">
                <BanknoteArrowDown size={20} className="mr-2 text-yellow-500" />
                <span className="text-sm font-medium">Liabilities:</span>
                <span className="ml-auto">
                  {formatCurrency(basicInfo.liabilities)}
                </span>
              </Typography>
            )}

            {basicInfo.non_liquid_assets && (
              <Typography className="flex items-center text-gray-600">
                <Building2 size={20} className="mr-2 text-indigo-500" />
                <span className="text-sm font-medium">Non-Liquid Assets:</span>
                <span className="ml-auto">
                  {formatCurrency(basicInfo.non_liquid_assets)}
                </span>
              </Typography>
            )}
            {client.recap && (
              <Typography className="flex items-center text-gray-600">
                <Clock className="mr-2 h-5 w-5 text-blue-500" />
                <span className="text-sm font-medium">Created At:</span>
                <AfterHydration>
                  <span className="ml-auto">
                    {format(
                      new Date(client.recap.createdAt),
                      "ccc, MMM do, h:mm aaa"
                    )}
                  </span>
                </AfterHydration>
              </Typography>
            )}
          </div>
        )}

        {DemoClientMockDataEnabled && (
          <div className="flex flex-col gap-4">
            {mockData.tags.length > 0 && (
              <div className="flex items-center gap-2">
                <Tag className="text-gray-500" />
                <div className="flex flex-wrap gap-2">
                  {mockData.tags!.map((tag) => (
                    <Badge
                      key={`a-${tag}`}
                      className="cursor-default bg-gray-100 text-xs text-gray-800 hover:text-white"
                    >
                      {capitalize(tag)}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            <div className="grid grid-cols-2 gap-2">
              <Typography className="flex items-center text-gray-600">
                <DollarSign size={20} className="mr-2 text-green-500" />
                <span className="text-sm font-medium">Annual Income:</span>
                <span className="ml-auto">{mockData.annual_income}</span>
              </Typography>

              <Typography className="flex items-center text-gray-600">
                <Landmark size={20} className="mr-2 text-blue-500" />
                <span className="text-sm font-medium">Net Worth:</span>
                <span className="ml-auto">{mockData.net_worth}</span>
              </Typography>

              <Typography className="flex items-center text-gray-600">
                <CircleCheckBig size={20} className="mr-2 text-yellow-500" />
                <span className="text-sm font-medium">
                  Task Completion Rate:
                </span>
                <span className="ml-auto">{mockData.task_completion_rate}</span>
              </Typography>

              <Typography className="flex items-center text-gray-600">
                <ThumbsUp size={20} className="mr-2 text-indigo-500" />
                <span className="text-sm font-medium">
                  Client Satisfaction Score:
                </span>
                <span className="ml-auto">
                  {mockData.client_satisfaction_score}
                </span>
              </Typography>

              <Typography className="flex items-center text-gray-600">
                <Calendar size={20} className="mr-2 text-red-500" />
                <span className="text-sm font-medium">Last Activity:</span>
                <span className="ml-auto">{mockData.lastActivity}</span>
              </Typography>
            </div>
          </div>
        )}

        <Separator className="my-2" />

        <ClientTabGroup
          notesTab={<NotesTab notes={client.notes} />}
          tasksTab={
            <TasksTab
              tasks={
                (client.actionItems ??
                  []) as unknown as SerializeFrom<TaskResponse>[]
              }
            />
          }
          clientRecapTab={<ClientRecapTab recap={client.recap} />}
        />
      </div>
    </SidebarV2>
  );
};

export default Route;
